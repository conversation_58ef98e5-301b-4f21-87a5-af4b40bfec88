/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
@import '~antd/lib/style/themes/default.less';

.item {
  display: flex;
  flex-direction: column;
  gap: 5px;
  align-items: center;
  justify-content: center;
  padding: 10px;
  border: 1px solid #e6e6e6;
  border-radius: 5px;
}

.image {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.accountCard {
  height: 180px;
  position: relative;
  &Footer {
    position: absolute;
    right: 0;
    bottom: 0;

    &Item {
      height: 100px;
      background-color: @select-item-selected-bg;
      border-radius: @border-radius-base;
      padding: @padding-xs @padding-sm;
      cursor: pointer;
      text-align: center;
      align-items: center;
      border: 1px solid @select-item-selected-bg;
      &:hover {
        border-color: @select-item-active-bg;
      }
      &Active {
        background-color: @select-item-active-bg;
      }
    }
  }
}
