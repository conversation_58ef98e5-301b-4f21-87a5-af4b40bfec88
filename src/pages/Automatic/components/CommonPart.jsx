/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable react/jsx-no-bind */
/**
 * 分拣线配置、格口配置、进港分拣、格口统计，公共页面
 * @param source 页面类型：sortLine（分拣线配置），grid（格口配置），arrivalSorting（进港分拣），gridStatistics（格口统计），
 *  */
import React, { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import {
  Form,
  Button,
  Popconfirm,
  Select,
  Row,
  Col,
  Input,
  DatePicker,
  message,
  Avatar,
  Modal,
  Icon,
} from 'antd';
import { connect, useHistory } from 'dva';
import { debounce, cloneDeep } from 'lodash';
import PropTypes from 'prop-types';
import moment from 'moment/moment';
import DragTable from '@/components/DragTable';
import { PhotoSlider } from 'react-photo-view';
import AsyncDownLoad from '@/components/DownloadBar/AsyncDownLoad';
import { useRequest } from 'ahooks';
import CommonModal from './CommonModal';
import SortingModelModal from './SortingModelModal';
import ColumnsSetting from '@/components/ColumnsSetting/ColumnsSetting';
import styles from './styles.less';
import 'react-photo-view/dist/index.css';
import UploadWithCrypto from '@/components/upload-width-crypto';
import { checkDispatch } from '@/services/automatic';
import KbPreviewImage from '@/components/KbPreviewImage';
// import { reportAnalytics } from '@/utils/utils';
import AuthorizedExtend, { checkAuthorized } from '@/components/Authorized/AuthorizedExtend';
import { getChannelList, reportFetchPic } from '@/services/advanced';
import { Yzs } from '@/components/Select';
import VirtualTable from '@/components/antd4/VirtualTable';
import { scanOption } from '../_utils';

const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;

const { MODE_ENV } = process.env;
// 判断是否是在本地服务器访问
const isLocal = MODE_ENV === 'AUTOMATIC';

const dataFormat = 'YYYY-MM-DD HH:mm:ss';

const formItemLayout = {
  labelCol: {
    sm: { span: 6 },
  },
  wrapperCol: {
    sm: { span: 18 },
  },
};

const config = {
  searchEffects: {
    // 分拣线配置
    sortLine: {
      type: 'automatic/getSortLineList',
      pageSize: 200,
    },
    // 格口配置
    grid: {
      type: 'automatic/getGridConfList',
      downloadURL: '/Api/Automation/GeKou/list',
      uploadURL: '/Api/Automation/GeKou/upload',
      pageSize: 20,
    },
    // 进港分拣
    arrivalSorting: {
      type: 'automatic/getArrivalSortingList',
      downloadURL: isLocal ? '/admin/SortLine/listArrival' : '/Api/Automation/SortLine/listArrival',
      pageSize: 20,
    },
    // 格口统计
    gridStatistics: {
      type: 'automatic/getGridStatisticsList',
      downloadURL: isLocal
        ? '/admin/SortLine/codeStatistic'
        : '/Api/Automation/GeKou/codeStatistic',
      pageSize: 20,
    },
  },
  deleteEffects: {
    sortLine: 'automatic/deleteSortLineList',
    grid: 'automatic/deleteGridConfig',
  },
  tableSource: {
    sortLine: 'sortLineList',
    grid: 'gridConfList',
    arrivalSorting: 'arrivalSortingList',
    gridStatistics: 'gridStatisticsList',
  },
};

// const baseAbnormalGridNameOptions = {
//   0: '综合异常口',
//   1: '拦截/通缉异常口',
//   2: '代收/到付异常口',
//   3: '送货上门件异常口',
//   4: '标快件异常口',
//   5: '错分异常口',
// };

const disabledDate = current =>
  current &&
  current >
    moment()
      .endOf('day')
      .subtract(0, 'days');

const defaultSelectDate = {
  startDate: moment()
    .startOf('day')
    .subtract(0, 'days'),
  endDate: moment()
    .endOf('day')
    .subtract(0, 'days'),
};

function ImageView({ src }) {
  const [visible, setVisible] = React.useState(false);
  const [photoIndex, setPhotoIndex] = React.useState(0);
  return (
    <div>
      <Avatar
        style={{ cursor: 'pointer' }}
        shape="square"
        onClick={() => setVisible(true)}
        icon="picture"
      />
      <PhotoSlider
        images={src.map(item => ({ src: item }))}
        visible={visible}
        onClose={() => setVisible(false)}
        index={photoIndex}
        onIndexChange={setPhotoIndex}
      />
    </div>
  );
}

const CommonPart = ({
  dispatch,
  form,
  loading,
  source,
  isOldUser,
  abnormalGridNameOptions,
  currentUser,
}) => {
  const { getFieldDecorator, validateFields, resetFields } = form;
  const { searchEffects, deleteEffects } = config;
  const isSortLine = source === 'sortLine';
  const isGrid = source === 'grid';
  const isArrivalSorting = source === 'arrivalSorting';
  const isGridStatistics = source === 'gridStatistics';
  const showForm = !isSortLine;

  const [selectedRow, setSelectedRow] = useState([]);
  const [modalType, setModalType] = useState('add');
  const [normal, setNormal] = useState(false);
  const [record, setRecord] = useState({});
  const [visible, setVisible] = useState(false);
  const [currentPage, setPage] = useState(1);
  // const [branchList, setBranchList] = useState([]);
  const [tableData, setTableData] = useState({ list: [], pagination: {} });
  const [gridSortLineList, setGirdLineList] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [modelModalShow, setModelModalShow] = useState(false);
  const [brands, setBrands] = useState([]);
  const [dispatchConfigLoading, setDispatchConfigLoading] = useState(false);
  const [processedColumns, setProcessedColumns] = useState(null);
  const initializedRef = useRef(false);
  const timerRef = useRef(null);
  const { is_preventError, user_info: { phone } = {} } = currentUser || {};

  const {
    data: { data: channelList = [] } = {},
    loading: channelListLoading,
  } = useRequest(getChannelList);

  const history = useHistory();

  const canAccess = checkAuthorized({ auth: '2', patchId: true });

  const onBrandChange = useCallback(
    () => {
      if (isGrid) {
        resetFields('branch_code');
      }
    },
    [isGrid, resetFields],
  );

  const getFormValues = useCallback(
    (then = () => {}) => {
      validateFields((err, values) => {
        if (err) return;
        const formData = { ...values };
        const {
          time,
          brand,
          sorting_line_id: sortingLineId,
          branch_code: branchCode,
          is_accuracy: isAccuracy,
          grid_type,
        } = formData;
        if (time) {
          const [start, end] = time;
          formData.scan_time_start = start.format(dataFormat);
          formData.scan_time_end = end.format(dataFormat);
          delete formData.time;
        }
        if (brand === '0') {
          formData.brand = undefined;
        }
        if (sortingLineId === 'all') {
          formData.sorting_line_id = undefined;
        }
        if (branchCode === 'all') {
          formData.branch_code = undefined;
        }
        if (isAccuracy === 'all') {
          formData.is_accuracy = undefined;
        }
        if (grid_type === 'all') {
          formData.grid_type = undefined;
        }
        then(formData);
      });
    },
    [validateFields],
  );

  const getList = useCallback(
    ({ page, pageSize, join_inn_id, down_inn_id, ...rest } = {}) => {
      const req = cloneDeep(rest);

      if (source === 'arrivalSorting' || source === 'gridStatistics') {
        if (req.is_accuracy === 'all') {
          req.is_accuracy = undefined;
        }
      }
      dispatch({
        type: searchEffects[source].type,
        payload: {
          page,
          size: pageSize || searchEffects[source].pageSize,
          pageSize: pageSize || searchEffects[source].pageSize,
          join_inn_id: join_inn_id === 'all' ? '' : join_inn_id,
          down_inn_id: down_inn_id === 'all' ? '' : down_inn_id,
          ...req,
        },
      }).then(data => {
        setTableData(data);
        setSelectedRow([]);
      });
    },
    [dispatch, source, searchEffects],
  );

  const onEdit = useCallback((data, type) => {
    const { grid_type: gridType } = data;
    setRecord(data);
    setModalType(type);
    setVisible(true);
    setNormal(gridType === '1');
  }, []);

  const onChangeModel = useCallback(data => {
    setRecord(data);
    setModelModalShow(true);
  }, []);

  const onDelete = useCallback(
    (id = []) => {
      dispatch({
        type: deleteEffects[source],
        payload: {
          id: id.join(','),
        },
      }).then(() => {
        getFormValues(values => {
          getList({
            page: currentPage,
            ...values,
          });
        });
      });
    },
    [source, dispatch, deleteEffects, getList, getFormValues, currentPage],
  );

  // 定义错误类型枚举
  const ERROR_TYPES = {
    THREE_CODE: '3code',
    ADDRESS: 'address',
  };

  // 定义来源类型枚举
  const SOURCE_TYPES = {
    OCR: 'ocr',
    THIRD: 'third',
  };

  // 定义错误映射配置
  const ERROR_CONFIG = {
    [ERROR_TYPES.THREE_CODE]: {
      [SOURCE_TYPES.OCR]: ['ocr_3code_query_error', 'ocr_3code_match_error'],
      [SOURCE_TYPES.THIRD]: ['api_3code_query_error', 'api_3code_match_error'],
    },
    [ERROR_TYPES.ADDRESS]: {
      [SOURCE_TYPES.OCR]: ['ocr_address_query_error', 'ocr_address_match_error'],
      [SOURCE_TYPES.THIRD]: ['ocr_address_query_error', 'ocr_address_match_error'],
      // [SOURCE_TYPES.THIRD]: ['api_address_query_error', 'api_address_match_error'],
    },
  };

  // 工具函数：获取表单类型
  const getFormType = (matchType, threeCodeForm, addressForm) => {
    if (matchType === ERROR_TYPES.THREE_CODE) return threeCodeForm;
    if (matchType === ERROR_TYPES.ADDRESS) return addressForm;
    return '';
  };

  // 工具函数：获取错误信息
  const getErrorMessage = matchData => {
    if (!matchData) return '';

    const { three_code_form, address_form, match_form, ...errorData } = matchData;

    const formType = getFormType(match_form, three_code_form, address_form);
    if (!formType || !ERROR_CONFIG[match_form]) return '';

    const errorKeys = ERROR_CONFIG[match_form][formType] || [];
    return errorKeys
      .map(key => errorData[key])
      .filter(Boolean)
      .join(', ');
  };

  const onPreview = ({ waybill, id }) => {
    // reportAnalytics({ label: '查看图片' });
    reportFetchPic({
      waybill,
      id,
    });
  };

  // 将操作列单独定义，避免循环依赖
  const actionColumn = useMemo(
    () => ({
      title: '操作',
      width: isSortLine ? 200 : 150,
      show: (isGrid || isSortLine) && canAccess,
      align: 'center',
      fixed: 'right',
      render: (_, data) => (
        <>
          <span key="edit" style={{ marginRight: 20 }}>
            <a onClick={() => onEdit(data, 'edit')}>编辑</a>
          </span>
          {isSortLine && (
            <>
              <span key="model" style={{ marginRight: 20 }}>
                <a onClick={() => onChangeModel(data)}>分拣模式</a>
              </span>
            </>
          )}
          <span key="delete">
            <Popconfirm title="确定删除该配置？" onConfirm={() => onDelete([data.id])}>
              <a>删除</a>
            </Popconfirm>
          </span>
        </>
      ),
    }),
    [isSortLine, isGrid, canAccess, onEdit, onChangeModel, onDelete],
  );

  const allColumns = useMemo(
    () => {
      const arr = [
        {
          title: '分拣线ID',
          dataIndex: 'id',
          key: 'id',
          width: 100,
          show: isSortLine,
          align: 'center',
        },
        {
          title: '分拣线编号',
          dataIndex: 'sorting_line_code',
          key: 'sorting_line_code',
          width: 180,
          show: true,
          align: 'center',
        },
        {
          title: '单号',
          dataIndex: 'waybill',
          key: 'waybill',
          width: 250,
          show: isArrivalSorting,
          align: 'center',
          render: (text, r) => {
            const { match_data: { file_path = '', file_path_online, id: _id } = {}, waybill, id } =
              r || {};
            let filePath = file_path;
            if (filePath.indexOf('local_pic') > -1) {
              filePath = `local_pic${_id}`;
            } else if (!filePath.startsWith('http')) {
              filePath = '';
            }
            return (
              <Row type="flex" justify="center" align="middle" gutter={[12, 0]}>
                <Col>{text}</Col>
                <Col>
                  {filePath ? (
                    <KbPreviewImage src={filePath} type="custom" unReg>
                      <Icon type="file-image" style={{ fontSize: '28px' }} />
                    </KbPreviewImage>
                  ) : file_path_online == 1 ? (
                    <Icon
                      type="file-image"
                      style={{ fontSize: '28px' }}
                      onClick={() => onPreview({ waybill, id })}
                    />
                  ) : null}
                </Col>
              </Row>
            );
          },
        },
        {
          title: '分拣线名称',
          dataIndex: 'sorting_line_name',
          key: 'sorting_line_name',
          width: 150,
          show: isSortLine,
          align: 'center',
        },
        {
          title: '设备SN码',
          dataIndex: 'sn_code',
          key: 'sn_code',
          width: 150,
          show: isSortLine,
          align: 'center',
        },
        {
          title: '分拣线功能',
          dataIndex: 'sorting_line_type',
          key: 'sorting_line_type',
          width: 150,
          show: isSortLine,
          align: 'center',
          render: text => (text === '1' ? '进港' : '出港'),
        },
        {
          title: '快递品牌',
          dataIndex: 'brand',
          key: 'brand',
          width: 150,
          show: isGrid || isGridStatistics || isArrivalSorting,
          align: 'center',
          render: brandsInList => {
            if (brandsInList) {
              const names = [];
              brandsInList.split(',').forEach(brand => {
                const { name } = brands.find(v => v.brand === brand) || {};
                names.push(name);
              });
              return names.join('，');
            }
            return isArrivalSorting ? '暂无品牌' : '全品牌';
          },
        },
        {
          title: '图片',
          dataIndex: 'img',
          key: 'img',
          width: 150,
          show: isArrivalSorting && isLocal,
          align: 'center',
          render: img => <ImageView src={[img]} />,
        },
        {
          title: '分拣线品牌',
          dataIndex: 'brand',
          key: 'brand',
          width: 150,
          show: isSortLine,
          align: 'center',
          render: brandsInList => {
            if (brandsInList) {
              const names = [];
              brandsInList.split(',').forEach(brand => {
                const { name } = brands.find(v => v.brand === brand) || {};
                name && names.push(name);
              });
              return names.join('，');
            }
            return '暂无品牌';
          },
        },
        {
          title: '默认上线扫描员',
          dataIndex: 'up_scanner',
          key: 'up_scanner',
          width: 150,
          show: isSortLine,
          align: 'center',
        },
        {
          title: '默认下线扫描员',
          dataIndex: 'down_scanner',
          key: 'down_scanner',
          width: 150,
          show: isSortLine,
          align: 'center',
        },
        {
          title: '分拣辅助扫描员',
          dataIndex: 'scanner_user_object_list',
          key: 'scanner_user_object_list',
          width: 150,
          show: isSortLine,
          align: 'center',
          render: (
            _,
            { scanner_user_object_list: scannerList, scanner_user_object_count: scannerCount },
          ) => {
            if (Array.isArray(scannerList) && scannerList.length) {
              const _scannerList = scannerList.reduce((pre, cur) => {
                if (Array.isArray(cur.user_object_list)) {
                  return pre.concat(cur.user_object_list.map(item => `${item}`));
                }
                return pre;
              }, []);
              return _scannerList.join(',');
            }
            return `${scannerCount}个`;
          },
        },
        {
          title: '是否已开启',
          dataIndex: 'status',
          key: 'status',
          width: 110,
          align: 'center',
          show: isSortLine,
          render: status => (status === '1' ? '否' : '是'),
        },
        {
          title: '网点编号',
          dataIndex: 'branch_code',
          key: 'branch_code',
          width: 150,
          show: isGrid,
          align: 'center',
        },
        {
          title: '格口类型',
          dataIndex: 'grid_type',
          key: 'grid_type',
          width: 150,
          show: isGridStatistics,
          align: 'center',
          render: gridType => {
            if (gridType === '1') {
              return '正常格口';
            }
            if (gridType === '2') {
              return '异常格口';
            }
            return '-';
          },
        },
        {
          title: '格口编号',
          dataIndex: 'grid_code',
          key: 'grid_code',
          width: 150,
          show: isGrid || isGridStatistics,
          align: 'center',
        },
        {
          title: '实际落格件量',
          dataIndex: 'sj_num',
          key: 'sj_num',
          width: 150,
          show: isGridStatistics,
          align: 'center',
        },
        {
          title: '应分配落格件量',
          dataIndex: 'fp_num',
          key: 'fp_num',
          width: 150,
          show: isGridStatistics,
          align: 'center',
        },
        {
          title: '格口名称',
          dataIndex: 'grid_name',
          key: 'grid_name',
          width: 150,
          show: isGrid,
          align: 'center',
        },
        {
          title: '下线扫描',
          dataIndex: 'down_scanner_type',
          key: 'down_scanner_type',
          width: 150,
          show: isGrid,
          align: 'center',
          render: text => scanOption.find(v => v.key == text)?.value || '-',
        },
        {
          title: '下节点操作对象',
          dataIndex: 'down_scanner_object',
          key: 'down_scanner_object',
          width: 150,
          show: isGrid,
          align: 'center',
          render: (text, { downScannerObjectName }) =>
            `${downScannerObjectName ? `${downScannerObjectName} - ` : ''}${text}`,
        },
        {
          title: '下一站派件员',
          dataIndex: 'lower_node_dispatch',
          key: 'lower_node_dispatch',
          width: 150,
          show: isGrid,
          align: 'center',
          render: (text, { lowerNodeDispatchName }) =>
            `${lowerNodeDispatchName ? `${lowerNodeDispatchName} - ` : ''}${text}`,
        },
        {
          title: '分拣驿站',
          dataIndex: 'down_inn_phone',
          key: 'down_inn_phone',
          width: 150,
          show: isGrid,
          align: 'center',
          render: (_, { down_inn_name, down_inn_phone }) =>
            `${down_inn_name ? `${down_inn_name} - ` : ''}${down_inn_phone || ''}`,
        },
        {
          title: '第一段码',
          dataIndex: 'one_code',
          key: 'one_code',
          width: 110,
          show: isGrid || isArrivalSorting,
          align: 'center',
        },
        {
          title: '第二段码',
          dataIndex: 'two_code',
          key: 'two_code',
          width: 110,
          show: isGrid || isArrivalSorting,
          align: 'center',
        },
        {
          title: '第三段码',
          dataIndex: 'three_code',
          key: 'three_code',
          width: 110,
          show: isGrid || isArrivalSorting,
          align: 'center',
        },
        {
          title: '第四段码',
          dataIndex: 'four_code',
          key: 'four_code',
          width: 110,
          show: is_preventError && (isGrid || isArrivalSorting),
          align: 'center',
        },
        {
          title: '分拣驿站',
          dataIndex: 'down_inn_phone',
          key: 'down_inn_phone',
          width: 150,
          show: isArrivalSorting,
          align: 'center',
          render: (_, { join_inn_name, join_inn_phone }) =>
            `${join_inn_name ? `${join_inn_name} - ` : ''}${join_inn_phone || ''}`,
        },
        {
          title: '入库驿站',
          dataIndex: 'down_inn_name',
          width: 150,
          show: isArrivalSorting,
          align: 'center',
          render: (_, { waybill }) => (
            <Button
              type="link"
              size="small"
              onClick={() => history.push(`/post/advanced?waybill=${waybill}`)}
            >
              查询
            </Button>
          ),
        },
        {
          title: '入库驿站状态',
          dataIndex: 'join_inn_status',
          key: 'join_inn_status',
          width: 150,
          show: isArrivalSorting,
          align: 'center',
          render: (_, { join_inn_status }) => {
            if (join_inn_status === '1') {
              return '已入库';
            }
            return '未入库';
          },
        },
        {
          title: '入库驿站时间',
          dataIndex: 'join_inn_time',
          key: 'join_inn_time',
          width: 150,
          show: isArrivalSorting,
          align: 'center',
        },
        {
          title: '来源',
          dataIndex: 'source',
          key: 'source',
          width: 80,
          show: isArrivalSorting,
          align: 'center',
          render: (_, r) => {
            const { match_data: { three_code_form, match_form } = {} } = r;
            return match_form === ERROR_TYPES.ADDRESS
              ? '地址'
              : match_form === ERROR_TYPES.THREE_CODE
                ? three_code_form == SOURCE_TYPES.OCR
                  ? '拍图段码'
                  : three_code_form == SOURCE_TYPES.THIRD
                    ? '接口段码'
                    : '-'
                : '-';
          },
        },
        {
          title: '地址关键字',
          dataIndex: 'addr_keys',
          key: 'addr_keys',
          width: 220,
          show: (isGrid && isOldUser) || isArrivalSorting,
          align: 'center',
          render: addr_keys => {
            if (addr_keys && addr_keys.length > 50) {
              return `${addr_keys.substring(0, 50)}...`;
            }
            return addr_keys;
          },
        },
        {
          title: '重量',
          dataIndex: 'weight',
          key: 'weight',
          width: 110,
          show: isArrivalSorting,
          align: 'center',
        },
        {
          title: '渠道',
          dataIndex: 'channel',
          key: 'channel',
          width: 110,
          show: isArrivalSorting,
          align: 'center',
          render: text => {
            return channelList.find(v => v.value == text)?.name || text;
          },
        },
        {
          title: '分配格口号',
          dataIndex: 'grid_code',
          key: 'grid_code',
          width: 110,
          show: isArrivalSorting,
          align: 'center',
        },
        {
          title: '实际格口号',
          dataIndex: 'actual_grid_code',
          key: 'actual_grid_code',
          width: 110,
          show: isArrivalSorting,
          align: 'center',
        },
        {
          title: '是否准确落格',
          dataIndex: 'is_accuracy',
          key: 'is_accuracy',
          width: 120,
          show: isArrivalSorting,
          align: 'center',
          render: isAccuracy => {
            if (isAccuracy === '0') {
              return '-';
            }
            if (isAccuracy === '1') {
              return '准确落格';
            }
            return '错误落格';
          },
        },
        {
          title: '识别状态',
          dataIndex: 'gbt_status',
          key: 'gbt_status',
          width: 120,
          show: isArrivalSorting,
          align: 'center',
        },
        {
          title: '未获取原因',
          dataIndex: 'no_reason',
          key: 'no_reason',
          width: 120,
          show: isArrivalSorting,
          align: 'center',
          render: (_, r) => {
            const { gbt_status, match_data = {} } = r;
            if (gbt_status == '正常') return '';
            return getErrorMessage(match_data);
          },
        },
        {
          title: '供包台编号',
          dataIndex: 'gbt_no',
          key: 'gbt_no',
          width: 110,
          show: isArrivalSorting,
          align: 'center',
        },
        {
          title: '供包台扫描时间',
          dataIndex: 'gbt_scan_time',
          key: 'gbt_scan_time',
          width: 200,
          show: isArrivalSorting,
          align: 'center',
        },
        {
          title: '本级到件上传时间',
          dataIndex: 'dj_time',
          key: 'dj_time',
          width: 200,
          show: isArrivalSorting,
          align: 'center',
        },
        {
          title: '本级到件上传状态',
          dataIndex: 'dj_status',
          key: 'dj_status',
          width: 100,
          show: isArrivalSorting,
          align: 'center',
          render: (status, { dj_time: djTime }) => {
            if (djTime) {
              if (status === '0') {
                return '未推送';
              }
              return '已推送';
            }
            return '';
          },
        },
        {
          title: '本级发往上传时间',
          dataIndex: 'send_time',
          key: 'send_time',
          width: 200,
          show: isArrivalSorting,
          align: 'center',
        },
        {
          title: '本级发往上传状态',
          dataIndex: 'send_status',
          key: 'send_status',
          width: 100,
          show: isArrivalSorting,
          align: 'center',
          render: (status, { send_time: sendTime }) => {
            if (sendTime) {
              if (status === '0') {
                return '未推送';
              }
              return '已推送';
            }
            return '';
          },
        },
        {
          title: '本级派件上传时间',
          dataIndex: 'pj_time',
          key: 'pj_time',
          width: 200,
          show: isArrivalSorting,
          align: 'center',
        },
        {
          title: '本级派件上传状态',
          dataIndex: 'pj_status',
          key: 'pj_status',
          width: 100,
          show: isArrivalSorting,
          align: 'center',
          render: (status, { pj_time: pjTime }) => {
            if (pjTime) {
              if (status === '0') {
                return '未推送';
              }
              return '已推送';
            }
            return '';
          },
        },
        {
          title: '下级到件上传时间',
          dataIndex: 'next_dj_time',
          key: 'next_dj_time',
          width: 200,
          show: isArrivalSorting,
          align: 'center',
        },
        {
          title: '下级到件上传状态',
          dataIndex: 'next_dj_status',
          key: 'next_dj_status',
          width: 100,
          show: isArrivalSorting,
          align: 'center',
          render: (status, { next_dj_time: nextDjTime }) => {
            if (nextDjTime) {
              if (status === '0') {
                return '未推送';
              }
              return '已推送';
            }
            return '';
          },
        },
        {
          title: '下级派件上传时间',
          dataIndex: 'next_pj_time',
          key: 'next_pj_time',
          width: 200,
          show: isArrivalSorting,
          align: 'center',
        },
        {
          title: '下级派件上传状态',
          dataIndex: 'next_pj_status',
          key: 'next_pj_status',
          width: 100,
          show: isArrivalSorting,
          align: 'center',
          render: (status, { next_pj_time: nextPjTime }) => {
            if (nextPjTime) {
              if (status === '0') {
                return '未推送';
              }
              return '已推送';
            }
            return '';
          },
        },
        {
          title: '落格时间',
          dataIndex: 'luoge_time',
          key: 'luoge_time',
          width: 200,
          show: isArrivalSorting,
          align: 'center',
        },
        // {
        //   title: '快速补码',
        //   dataIndex: 'quick_code',
        //   key: 'quick_code',
        //   width: 110,
        //   show: isGrid,
        //   align: 'center',
        // },
        {
          title: '创建时间',
          dataIndex: 'create_time',
          key: 'create_time',
          width: 200,
          show: isGrid,
          align: 'center',
        },
        {
          title: '更新时间',
          dataIndex: 'update_time',
          key: 'update_time',
          width: 200,
          show: isGrid,
          align: 'center',
        },
        // 添加操作列
        actionColumn,
      ];
      return arr.filter(i => i.show);
    },
    [
      isGrid,
      isSortLine,
      isArrivalSorting,
      isGridStatistics,
      brands,
      isOldUser,
      abnormalGridNameOptions,
      is_preventError,
      channelList,
      actionColumn,
    ],
  );

  // 根据用户设置过滤显示的列
  const columns = useMemo(
    () => {
      // 如果有处理后的列，直接使用
      if (processedColumns) {
        return processedColumns;
      }
      // 否则返回原始列
      return allColumns;
    },
    [allColumns, processedColumns],
  );

  const onSelectChange = useCallback(selectedRowKeys => {
    setSelectedRow(selectedRowKeys);
  }, []);

  const handleColumnsChange = useCallback(columnData => {
    // columnData 包含 rawSettings 和 processedColumns
    if (columnData && columnData.processedColumns) {
      setProcessedColumns(columnData.processedColumns);
    }
  }, []);

  const onSubmit = useCallback(
    debounce(
      () => {
        getFormValues(values => {
          getList({
            page: 1,
            ...values,
          });
        });
      },
      300,
      {
        leading: true,
        trailing: false,
      },
    ),
    [getFormValues, getList],
  );

  const onReset = useCallback(
    debounce(
      isRequest => {
        resetFields();
        if (isRequest) {
          onSubmit();
        }
      },
      300,
      {
        leading: true,
        trailing: false,
      },
    ),
    [resetFields, onSubmit],
  );

  const onAdd = useCallback((isNormal, type) => {
    setNormal(isNormal);
    setVisible(true);
    setRecord({});
    if (type) {
      setModalType(type);
    }
  }, []);

  const onTableChange = useCallback(
    pagination => {
      setPage(pagination.current);
      getFormValues(values => {
        getList({
          page: pagination.current,
          pageSize: pagination.pageSize,
          ...values,
        });
      });
    },
    [getFormValues, getList],
  );

  const downloadData = useCallback(
    type => {
      let data = {};
      getFormValues(values => {
        data = {
          ...values,
          down: 1,
        };
      });
      dispatch({
        type,
        payload: data,
      }).then(() => {
        Modal.info({
          title: '温馨提示',
          content: '导出数据申请已提交，请至【报表下载】处下载',
        });
      });
    },
    [getFormValues],
  );

  const downloadParams = useCallback(
    () => {
      let data = {};
      getFormValues(values => {
        data = {
          ...values,
          down: 1,
        };
      });
      return { ...data };
    },
    [getFormValues],
  );

  const onUpLoadChange = useCallback(info => {
    setUploading(true);
    if (info.file.status === 'done') {
      if (info.file.response.code != 0) {
        message.error(`${info.file.name} 上传失败：${info.file.response.msg}`);
      } else {
        message.success(`${info.file.name} 上传成功`);
      }
      setUploading(false);
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} 上传失败`);
      setUploading(false);
    }
  }, []);

  // 保存分拣模式
  const handleModelSet = useCallback(
    formValues => {
      dispatch({
        type: 'automatic/addSortLine',
        payload: { ...formValues },
      })
        .then(() => {
          message.success('保存成功！');
          getFormValues(values => {
            getList({
              page: currentPage,
              ...values,
            });
          });
        })
        .catch(() => {
          message.error('保存失败！');
        });
    },
    [dispatch, currentPage, getFormValues, getList],
  );

  // 格口配置下发
  const dispatchConfig = () => {
    // 清除可能存在的定时器
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }

    dispatch({
      type: 'automatic/dispatchConfig',
    })
      .then(() => {
        handleCheckDispatchConfig(15, true);
      })
      .catch(() => {
        setDispatchConfigLoading(false);
      });
  };

  const handleCheckDispatchConfig = (count, isInitialCall = true) => {
    if (isInitialCall && timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }

    if (!count) {
      setDispatchConfigLoading(false);
      message.error('下发超时，请稍后重试');
      return;
    }
    setDispatchConfigLoading(true);
    checkDispatch()
      .then(res => {
        if (res.code === 0 && res.data?.success == 1) {
          setDispatchConfigLoading(false);
          message.success(res.msg);
        } else {
          timerRef.current = setTimeout(() => {
            handleCheckDispatchConfig(count - 1, false);
          }, 5000);
        }
      })
      .catch(() => {
        if (timerRef.current) {
          clearTimeout(timerRef.current);
          timerRef.current = null;
        }
        setDispatchConfigLoading(false);
        message.error('检查配置下发状态失败');
      });
  };

  useEffect(
    () => {
      dispatch({
        type: 'automatic/getSortLineBrands',
      }).then(res => {
        setBrands(res);
      });
      if (source !== 'sortLine') {
        dispatch({
          type: 'automatic/getGridSortLineList',
        }).then(res => {
          setGirdLineList(res);
        });
      }
    },
    [dispatch, source],
  );

  // 单独的useEffect处理初始化查询，避免循环依赖
  useEffect(
    () => {
      if ((source === 'sortLine' || source === 'grid') && !initializedRef.current) {
        initializedRef.current = true;
        // 延迟执行，确保其他状态已经初始化完成
        const timer = setTimeout(() => {
          getFormValues(values => {
            getList({
              page: 1,
              ...values,
            });
          });
        }, 100);
        return () => clearTimeout(timer);
      }
    },
    [source], // 只依赖source
  );

  // 当 channelList 加载完成后，重新应用已保存的列设置
  useEffect(() => {
    if (isArrivalSorting && !channelListLoading && channelList.length > 0 && !processedColumns) {
      // 尝试从 localStorage 获取已保存的列设置
      const storageKey = `${source}_table_columns_${phone}`;
      const savedSettings = localStorage.getItem(storageKey);
      if (savedSettings) {
        try {
          const parsedSettings = JSON.parse(savedSettings);
          if (Array.isArray(parsedSettings)) {
            // 手动触发列设置的应用
            handleColumnsChange({
              rawSettings: parsedSettings,
              processedColumns: allColumns.filter(col => {
                const key = col.key || col.dataIndex;
                const setting = parsedSettings.find(s => s.key === key);
                return setting ? setting.selected : true;
              }),
            });
          }
        } catch (error) {
          console.warn('Failed to parse saved column settings:', error);
        }
      }
    }
  }, [isArrivalSorting, channelListLoading, channelList.length, processedColumns, source, phone, allColumns, handleColumnsChange]);

  useEffect(() => {
    dispatch({
      type: 'automatic/getAbnormalGridNameOptions',
    });
  }, []);

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
    };
  }, []);

  const width = useMemo(() => columns.reduce((pre, cur) => pre + cur.width, 0), [columns]);

  return (
    <div className={styles.tableWrapper}>
      {!showForm && (
        <AuthorizedExtend auth="2" patchId>
          <Row type="flex" justify="space-between">
            <Col>
              <Button type="primary" onClick={() => onAdd(false, 'add')}>
                新增分拣线
              </Button>
            </Col>
            <Col>
              <Button type="primary" onClick={() => onEdit({}, 'authCode')}>
                管理授权码
              </Button>
            </Col>
          </Row>
        </AuthorizedExtend>
      )}
      {showForm && (
        <Form layout="inline" {...formItemLayout} className={styles.form}>
          <Row gutter={[0, 20]}>
            {(isGridStatistics || isArrivalSorting) && (
              <Col xs={24} sm={12} md={8}>
                <FormItem label="供包台扫描时间">
                  {getFieldDecorator('time', {
                    initialValue: [defaultSelectDate.startDate, defaultSelectDate.endDate],
                  })(
                    <RangePicker
                      showTime={{ format: 'HH:mm:ss' }}
                      format="YYYY-MM-DD HH:mm:ss"
                      disabledDate={disabledDate}
                      placeholder={['请输入开始时间', '请输入结束时间']}
                      style={{ width: '100%' }}
                    />,
                  )}
                </FormItem>
              </Col>
            )}
            <Col xs={24} sm={12} md={8}>
              <FormItem label="分拣线">
                {getFieldDecorator('sorting_line_id', {
                  initialValue: 'all',
                })(
                  <Select>
                    <Option value="all" key="all">
                      全部
                    </Option>
                    {gridSortLineList.map(v => (
                      <Option value={v.id} key={v.id}>
                        {v.sorting_line_name}
                      </Option>
                    ))}
                  </Select>,
                )}
              </FormItem>
            </Col>
            {isArrivalSorting && (
              <Col xs={24} sm={12} md={8}>
                <FormItem label="渠道">
                  {getFieldDecorator('channel', {
                    initialValue: '',
                  })(
                    <Select>
                      <Option value="" key="">
                        全部
                      </Option>
                      {channelList.map(v => (
                        <Option value={v.value} key={v.value}>
                          {v.name}
                        </Option>
                      ))}
                    </Select>,
                  )}
                </FormItem>
              </Col>
            )}
            {isArrivalSorting && (
              <Col xs={24} sm={12} md={8}>
                <FormItem label="单号">
                  {getFieldDecorator('waybill')(<Input placeholder="请输入单号" allowClear />)}
                </FormItem>
              </Col>
            )}
            {isGridStatistics && (
              <>
                <Col xs={24} sm={12} md={8}>
                  <FormItem label="格口类型">
                    {getFieldDecorator('grid_type', {
                      initialValue: '',
                    })(
                      <Select>
                        <Option value="" key="">
                          全部
                        </Option>
                        <Option value="1" key="1">
                          正常口
                        </Option>
                        <Option value="2" key="2">
                          异常口
                        </Option>
                      </Select>,
                    )}
                  </FormItem>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <FormItem label="格口编号">
                    {getFieldDecorator('grid_code')(
                      <Input placeholder="请输入格口编号" allowClear />,
                    )}
                  </FormItem>
                </Col>
              </>
            )}
            <Col xs={24} sm={12} md={8}>
              <FormItem label="品牌">
                {getFieldDecorator('brand', {
                  initialValue: '0',
                })(
                  <Select onChange={onBrandChange}>
                    {brands.map(v => (
                      <Option value={v.brand} key={v.brand}>
                        {v.name}
                      </Option>
                    ))}
                  </Select>,
                )}
              </FormItem>
            </Col>
            {isArrivalSorting && (
              <>
                <Col xs={24} sm={12} md={8}>
                  <FormItem label="分配格口号">
                    {getFieldDecorator('grid_code')(
                      <Input placeholder="请输入分配格口号" allowClear />,
                    )}
                  </FormItem>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <FormItem label="实际格口号">
                    {getFieldDecorator('actual_grid_code')(
                      <Input placeholder="请输入实际格口号" allowClear />,
                    )}
                  </FormItem>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <FormItem label="是否准确落格">
                    {getFieldDecorator('is_accuracy', {
                      initialValue: 'all',
                    })(
                      <Select>
                        <Option value="all" key="all">
                          全部
                        </Option>
                        <Option value="1" key="1">
                          准确落格
                        </Option>
                        <Option value="2" key="2">
                          错误落格
                        </Option>
                      </Select>,
                    )}
                  </FormItem>
                </Col>
              </>
            )}
            {isGrid && (
              <>
                <Col xs={24} sm={12} md={8}>
                  <FormItem label="格口类型">
                    {getFieldDecorator('grid_type', {
                      initialValue: 'all',
                    })(
                      <Select>
                        <Option value="all" key="all">
                          全部类型
                        </Option>
                        <Option value="1" key="1">
                          正常口
                        </Option>
                        <Option value="2" key="2">
                          异常口
                        </Option>
                      </Select>,
                    )}
                  </FormItem>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <FormItem label="格口">
                    {getFieldDecorator('grid_code_or_name')(
                      <Input placeholder="请输入格口号或格口名称" />,
                    )}
                  </FormItem>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <FormItem label="下线扫描">
                    {getFieldDecorator('down_scanner_type', {
                      initialValue: '',
                    })(
                      <Select>
                        {[{ key: '', value: '全部类型' }, ...scanOption].map(({ key, value }) => (
                          <Option value={key} key={key}>
                            {value}
                          </Option>
                        ))}
                      </Select>,
                    )}
                  </FormItem>
                </Col>
              </>
            )}
            <Col xs={24} sm={12} md={8}>
              <FormItem label="驿站">
                {getFieldDecorator(isArrivalSorting ? 'join_inn_id' : 'down_inn_id', {
                  initialValue: 'all',
                })(
                  <Yzs
                    style={{ width: '100%' }}
                    showAll
                    repeatRequest={false}
                    requestInit={false}
                    placeholder="请选择具体驿站"
                  />,
                )}
              </FormItem>
            </Col>
            {!isGridStatistics && (
              <>
                <Col xs={24} sm={12} md={8}>
                  <FormItem label="第一段码">
                    {getFieldDecorator('one_code')(<Input placeholder="请输入第一段码" />)}
                  </FormItem>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <FormItem label="第二段码">
                    {getFieldDecorator('two_code')(<Input placeholder="请输入第二段码" />)}
                  </FormItem>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <FormItem label="第三段码">
                    {getFieldDecorator('three_code')(<Input placeholder="请输入第三段码" />)}
                  </FormItem>
                </Col>
                {is_preventError && (
                  <Col xs={24} sm={12} md={8}>
                    <FormItem label="第四段码">
                      {getFieldDecorator('four_code')(<Input placeholder="请输入第四段码" />)}
                    </FormItem>
                  </Col>
                )}
              </>
            )}
            {isArrivalSorting &&
              isOldUser && (
                <Col xs={24} sm={12} md={8}>
                  <FormItem label="地址关键字">
                    {getFieldDecorator('addr_keys')(<Input placeholder="请输入地址关键字" />)}
                  </FormItem>
                </Col>
              )}
          </Row>
          <Row type="flex" justify="space-between" style={{ marginTop: 20 }} gutter={[0, 20]}>
            <Col>
              <AuthorizedExtend auth="2" patchId>
                <Row type="flex" justify="end" gutter={[20, 20]}>
                  {isGrid && (
                    <>
                      <Col>
                        <Button type="primary" onClick={() => onAdd(false, 'add')}>
                          新增异常口
                        </Button>
                      </Col>
                      <Col>
                        <Button type="primary" onClick={() => onAdd(true, 'add')}>
                          新增正常口
                        </Button>
                      </Col>
                      <Col>
                        <UploadWithCrypto
                          action={searchEffects[source].uploadURL}
                          name="file"
                          multiple={false}
                          accept=".xlsx,.xls,csv"
                          showUploadList={false}
                          onChange={onUpLoadChange}
                        >
                          <Button type="primary" icon="upload" loading={uploading}>
                            导入配置
                          </Button>
                        </UploadWithCrypto>
                      </Col>
                      <Col>
                        <Button
                          loading={dispatchConfigLoading}
                          type="primary"
                          onClick={dispatchConfig}
                        >
                          下发配置
                        </Button>
                      </Col>
                    </>
                  )}
                  <Col>
                    {source == 'grid' ? (
                      <AsyncDownLoad
                        action={searchEffects[source].downloadURL}
                        method="POST"
                        data={downloadParams}
                      >
                        <Button type="primary" icon="download">
                          导出
                        </Button>
                      </AsyncDownLoad>
                    ) : (
                      <Button
                        type="primary"
                        icon="download"
                        onClick={downloadData.bind(null, searchEffects[source].type)}
                      >
                        导出
                      </Button>
                    )}
                  </Col>
                </Row>
              </AuthorizedExtend>
            </Col>
            <Col>
              <Row type="flex" justify="space-around" gutter={[20, 20]}>
                <Col>
                  <Button type="primary" onClick={onSubmit}>
                    查询
                  </Button>
                </Col>
                <Col>
                  <Button onClick={onReset}>重置</Button>
                </Col>
                {isArrivalSorting && !channelListLoading && (
                  <Col>
                    <ColumnsSetting
                      columns={allColumns}
                      storageKey={`${source}_table_columns_${phone}`}
                      onColumnsChange={handleColumnsChange}
                    />
                  </Col>
                )}
              </Row>
            </Col>
          </Row>
        </Form>
      )}
      <div style={{ margin: '10px 0', fontSize: 14 }}>
        {tableData.eq != 1 &&
          tableData.list.length > 0 &&
          tableData.grid_code_or_name && (
            <>
              未查询到&quot;
              {tableData.grid_code_or_name}
              &quot;格口，以下是关键词匹配的近似结果
            </>
          )}
      </div>
      <DragTable>
        <VirtualTable
          rowKey="id"
          columns={columns}
          dataSource={tableData.list}
          pagination={
            !isSortLine
              ? {
                  current: tableData.pagination.current,
                  pageSize: tableData.pagination.pageSize || 20,
                  total: tableData.pagination.total,
                  page: tableData.pagination.current,
                  showTotal: () => `共 ${tableData.pagination.total} 条`,
                  showSizeChanger: true,
                  pageSizeOptions: ['20', '50', '200'],
                  showQuickJumper: true,
                  hideOnSinglePage: false,
                }
              : false
          }
          loading={loading}
          scroll={{ x: width, y: 500 }}
          rowHeight={117}
          onChange={onTableChange}
          rowSelection={
            isGrid && canAccess
              ? {
                  selectedRowKeys: selectedRow,
                  onChange: onSelectChange,
                  hideDefaultSelections: true,
                }
              : null
          }
        />
      </DragTable>
      {(isGrid || isSortLine) && (
        <>
          <SortingModelModal
            record={record}
            visible={modelModalShow}
            handleSubmit={handleModelSet}
            onCancel={() => setModelModalShow(false)}
            onSuccess={getList}
          />
          <CommonModal
            isOldUser={isOldUser}
            isNormal={normal}
            visible={visible}
            source={source}
            type={modalType}
            getList={modalType === 'edit' ? onSubmit : () => onReset(true)}
            onClose={() => setVisible(false)}
            record={record}
          />
        </>
      )}
      {isGrid && (
        <AuthorizedExtend auth="2" patchId>
          <Popconfirm
            style={{ width: '100px' }}
            title="确认批量删除选中的配置？"
            onConfirm={() => onDelete(selectedRow)}
            disabled={selectedRow.length === 0}
          >
            <span className={styles.deleteAll}>
              <Button
                style={{ width: '100px' }}
                type="link"
                block
                disabled={selectedRow.length === 0}
              >
                批量删除
              </Button>
            </span>
          </Popconfirm>
        </AuthorizedExtend>
      )}
    </div>
  );
};

CommonPart.defaultProps = {
  source: 'sortLine',
};

CommonPart.propTypes = {
  source: PropTypes.oneOf(['sortLine', 'grid', 'arrivalSorting', 'gridStatistics']),
};

export default connect(({ loading, automatic, user }, { source }) => ({
  tableData: automatic[config.tableSource[source]],
  loading: loading.effects[config.searchEffects[source].type],
  abnormalGridNameOptions: automatic.abnormalGridNameOptions,
  currentUser: user.currentUser,
}))(Form.create()(React.memo(CommonPart)));
